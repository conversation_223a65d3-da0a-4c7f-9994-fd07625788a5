import {
  AbstractControl,
  ValidatorFn,
  AsyncValidatorFn,
  Validators,
} from '@angular/forms';

export interface ValidatorRef {
  name: string;
  validator: ValidatorFn | AsyncValidatorFn;
  type: 'sync' | 'async';
}

export class ValidatorManager {
  private readonly validators = new WeakMap<
    AbstractControl,
    Map<string, ValidatorFn>
  >();
  private readonly asyncValidators = new WeakMap<
    AbstractControl,
    Map<string, AsyncValidatorFn>
  >();
  private readonly originalValidators = new WeakMap<
    AbstractControl,
    ValidatorFn | null
  >();
  private readonly originalAsyncValidators = new WeakMap<
    AbstractControl,
    AsyncValidatorFn | null
  >();

  public initializeControl(control: AbstractControl): void {
    if (!this.validators.has(control)) {
      this.validators.set(control, new Map());
      this.asyncValidators.set(control, new Map());
      this.originalValidators.set(control, control.validator);
      this.originalAsyncValidators.set(control, control.asyncValidator);
    }
  }

  public addValidator(
    control: AbstractControl,
    name: string,
    validator: ValidatorFn
  ): void {
    this.initializeControl(control);

    const validatorMap = this.validators.get(control)!;
    validatorMap.set(name, validator);

    this.updateControlValidators(control);
  }

  public removeValidator(control: AbstractControl, name: string): void {
    const validatorMap = this.validators.get(control);
    if (!validatorMap) return;

    validatorMap.delete(name);
    this.updateControlValidators(control);
  }

  public addAsyncValidator(
    control: AbstractControl,
    name: string,
    validator: AsyncValidatorFn
  ): void {
    this.initializeControl(control);

    const asyncValidatorMap = this.asyncValidators.get(control)!;
    asyncValidatorMap.set(name, validator);

    this.updateControlAsyncValidators(control);
  }

  public removeAsyncValidator(control: AbstractControl, name: string): void {
    const asyncValidatorMap = this.asyncValidators.get(control);
    if (!asyncValidatorMap) return;

    asyncValidatorMap.delete(name);
    this.updateControlAsyncValidators(control);
  }

  public hasValidator(control: AbstractControl, name: string): boolean {
    const validatorMap = this.validators.get(control);
    return validatorMap ? validatorMap.has(name) : false;
  }

  public hasAsyncValidator(control: AbstractControl, name: string): boolean {
    const asyncValidatorMap = this.asyncValidators.get(control);
    return asyncValidatorMap ? asyncValidatorMap.has(name) : false;
  }

  public getValidators(control: AbstractControl): string[] {
    const validatorMap = this.validators.get(control);
    return validatorMap ? Array.from(validatorMap.keys()) : [];
  }

  public getAsyncValidators(control: AbstractControl): string[] {
    const asyncValidatorMap = this.asyncValidators.get(control);
    return asyncValidatorMap ? Array.from(asyncValidatorMap.keys()) : [];
  }

  public clearValidators(control: AbstractControl): void {
    const validatorMap = this.validators.get(control);
    if (validatorMap) {
      validatorMap.clear();
      control.setValidators(null);
      control.updateValueAndValidity();
    }
  }

  public clearAsyncValidators(control: AbstractControl): void {
    const asyncValidatorMap = this.asyncValidators.get(control);
    if (asyncValidatorMap) {
      asyncValidatorMap.clear();
      control.setAsyncValidators(null);
      control.updateValueAndValidity();
    }
  }

  public clearAll(control: AbstractControl): void {
    this.clearValidators(control);
    this.clearAsyncValidators(control);
  }

  private updateControlValidators(control: AbstractControl): void {
    const validatorMap = this.validators.get(control);
    if (!validatorMap || validatorMap.size === 0) {
      control.setValidators(null);
    } else {
      const validators = Array.from(validatorMap.values());
      const composed =
        validators.length === 1
          ? validators[0]
          : Validators.compose(validators);
      control.setValidators(composed);
    }
    control.updateValueAndValidity();
  }

  private updateControlAsyncValidators(control: AbstractControl): void {
    const asyncValidatorMap = this.asyncValidators.get(control);
    if (!asyncValidatorMap || asyncValidatorMap.size === 0) {
      control.setAsyncValidators(null);
    } else {
      const validators = Array.from(asyncValidatorMap.values());
      const composed =
        validators.length === 1
          ? validators[0]
          : Validators.composeAsync(validators);
      control.setAsyncValidators(composed);
    }
    control.updateValueAndValidity();
  }

  public restoreOriginal(control: AbstractControl): void {
    const original = this.originalValidators.get(control);
    const originalAsync = this.originalAsyncValidators.get(control);

    if (original !== undefined) {
      control.setValidators(original);
    }
    if (originalAsync !== undefined) {
      control.setAsyncValidators(originalAsync);
    }

    control.updateValueAndValidity();

    this.validators.delete(control);
    this.asyncValidators.delete(control);
    this.originalValidators.delete(control);
    this.originalAsyncValidators.delete(control);
  }

  public cloneValidators(
    source: AbstractControl,
    target: AbstractControl
  ): void {
    const sourceValidators = this.validators.get(source);
    const sourceAsyncValidators = this.asyncValidators.get(source);

    if (sourceValidators) {
      const targetValidators = new Map(sourceValidators);
      this.validators.set(target, targetValidators);
    }

    if (sourceAsyncValidators) {
      const targetAsyncValidators = new Map(sourceAsyncValidators);
      this.asyncValidators.set(target, targetAsyncValidators);
    }

    this.updateControlValidators(target);
    this.updateControlAsyncValidators(target);
  }
}
