Phase 2: Field Management Services
5. FieldRegistry Service
typescript// core/services/field-registry.service.ts

import { Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { BehaviorSubject, Observable } from 'rxjs';
import { IField } from '../../interfaces';

export interface FieldRegistration {
  field: IField;
  control: AbstractControl;
  path: string;
  parentId?: string;
  childrenIds: string[];
  metadata: Map<string, any>;
}

export interface FieldLookup {
  byId: Map<string, FieldRegistration>;
  byPath: Map<string, string>;
  byName: Map<string, Set<string>>;
  byType: Map<string, Set<string>>;
}

@Injectable({
  providedIn: 'root'
})
export class FieldRegistry {
  private registrations = new Map<string, FieldRegistration>();
  private lookup: FieldLookup = {
    byId: new Map(),
    byPath: new Map(),
    byName: new Map(),
    byType: new Map()
  };
  
  private registrySubject = new BehaviorSubject<Map<string, FieldRegistration>>(new Map());
  public registry$ = this.registrySubject.asObservable();
  
  private changeSubject = new BehaviorSubject<string[]>([]);
  public changes$ = this.changeSubject.asObservable();

  public register(field: IField, control: AbstractControl, path: string, parentId?: string): string {
    const registration: FieldRegistration = {
      field,
      control,
      path,
      parentId,
      childrenIds: [],
      metadata: new Map()
    };
    
    this.registrations.set(field.id, registration);
    this.lookup.byId.set(field.id, registration);
    this.lookup.byPath.set(path, field.id);
    
    if (!this.lookup.byName.has(field.name)) {
      this.lookup.byName.set(field.name, new Set());
    }
    this.lookup.byName.get(field.name)!.add(field.id);
    
    if (!this.lookup.byType.has(field.type)) {
      this.lookup.byType.set(field.type, new Set());
    }
    this.lookup.byType.get(field.type)!.add(field.id);
    
    if (parentId) {
      const parent = this.registrations.get(parentId);
      if (parent) {
        parent.childrenIds.push(field.id);
      }
    }
    
    this.notifyChange([field.id]);
    return field.id;
  }

  public unregister(fieldId: string): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;
    
    registration.childrenIds.forEach(childId => this.unregister(childId));
    
    this.registrations.delete(fieldId);
    this.lookup.byId.delete(fieldId);
    this.lookup.byPath.delete(registration.path);
    
    const nameSet = this.lookup.byName.get(registration.field.name);
    if (nameSet) {
      nameSet.delete(fieldId);
      if (nameSet.size === 0) {
        this.lookup.byName.delete(registration.field.name);
      }
    }
    
    const typeSet = this.lookup.byType.get(registration.field.type);
    if (typeSet) {
      typeSet.delete(fieldId);
      if (typeSet.size === 0) {
        this.lookup.byType.delete(registration.field.type);
      }
    }
    
    if (registration.parentId) {
      const parent = this.registrations.get(registration.parentId);
      if (parent) {
        const index = parent.childrenIds.indexOf(fieldId);
        if (index > -1) {
          parent.childrenIds.splice(index, 1);
        }
      }
    }
    
    this.notifyChange([fieldId]);
    return true;
  }

  public get(fieldId: string): FieldRegistration | undefined {
    return this.registrations.get(fieldId);
  }

  public getByPath(path: string): FieldRegistration | undefined {
    const fieldId = this.lookup.byPath.get(path);
    return fieldId ? this.registrations.get(fieldId) : undefined;
  }

  public getByName(name: string): FieldRegistration[] {
    const ids = this.lookup.byName.get(name);
    if (!ids) return [];
    
    return Array.from(ids)
      .map(id => this.registrations.get(id))
      .filter(reg => reg !== undefined) as FieldRegistration[];
  }

  public getByType(type: string): FieldRegistration[] {
    const ids = this.lookup.byType.get(type);
    if (!ids) return [];
    
    return Array.from(ids)
      .map(id => this.registrations.get(id))
      .filter(reg => reg !== undefined) as FieldRegistration[];
  }

  public getControl(fieldId: string): AbstractControl | undefined {
    return this.registrations.get(fieldId)?.control;
  }

  public getField(fieldId: string): IField | undefined {
    return this.registrations.get(fieldId)?.field;
  }

  public getChildren(parentId: string): FieldRegistration[] {
    const parent = this.registrations.get(parentId);
    if (!parent) return [];
    
    return parent.childrenIds
      .map(id => this.registrations.get(id))
      .filter(reg => reg !== undefined) as FieldRegistration[];
  }

  public getParent(fieldId: string): FieldRegistration | undefined {
    const registration = this.registrations.get(fieldId);
    if (!registration?.parentId) return undefined;
    
    return this.registrations.get(registration.parentId);
  }

  public getSiblings(fieldId: string): FieldRegistration[] {
    const parent = this.getParent(fieldId);
    if (!parent) return [];
    
    return parent.childrenIds
      .filter(id => id !== fieldId)
      .map(id => this.registrations.get(id))
      .filter(reg => reg !== undefined) as FieldRegistration[];
  }

  public updateField(fieldId: string, updates: Partial<IField>): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;
    
    Object.assign(registration.field, updates);
    this.notifyChange([fieldId]);
    return true;
  }

  public setMetadata(fieldId: string, key: string, value: any): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;
    
    registration.metadata.set(key, value);
    this.notifyChange([fieldId]);
    return true;
  }

  public getMetadata(fieldId: string, key: string): any {
    return this.registrations.get(fieldId)?.metadata.get(key);
  }

  public deleteMetadata(fieldId: string, key: string): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;
    
    const deleted = registration.metadata.delete(key);
    if (deleted) {
      this.notifyChange([fieldId]);
    }
    return deleted;
  }

  public findFields(predicate: (registration: FieldRegistration) => boolean): FieldRegistration[] {
    const results: FieldRegistration[] = [];
    
    this.registrations.forEach(registration => {
      if (predicate(registration)) {
        results.push(registration);
      }
    });
    
    return results;
  }

  public getRequiredFields(): FieldRegistration[] {
    return this.findFields(reg => reg.field.required === true);
  }

  public getDisabledFields(): FieldRegistration[] {
    return this.findFields(reg => reg.control.disabled);
  }

  public getInvalidFields(): FieldRegistration[] {
    return this.findFields(reg => reg.control.invalid && reg.control.touched);
  }

  public getAllFields(): FieldRegistration[] {
    return Array.from(this.registrations.values());
  }

  public getFieldTree(rootId?: string): any {
    if (rootId) {
      const root = this.registrations.get(rootId);
      if (!root) return null;
      return this.buildFieldNode(root);
    }
    
    const roots = this.findFields(reg => !reg.parentId);
    return roots.map(root => this.buildFieldNode(root));
  }

  private buildFieldNode(registration: FieldRegistration): any {
    return {
      id: registration.field.id,
      name: registration.field.name,
      type: registration.field.type,
      path: registration.path,
      valid: registration.control.valid,
      value: registration.control.value,
      children: registration.childrenIds.map(childId => {
        const child = this.registrations.get(childId);
        return child ? this.buildFieldNode(child) : null;
      }).filter(node => node !== null)
    };
  }

  public moveField(fieldId: string, newParentId?: string, index?: number): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;
    
    if (registration.parentId) {
      const oldParent = this.registrations.get(registration.parentId);
      if (oldParent) {
        const idx = oldParent.childrenIds.indexOf(fieldId);
        if (idx > -1) {
          oldParent.childrenIds.splice(idx, 1);
        }
      }
    }
    
    registration.parentId = newParentId;
    
    if (newParentId) {
      const newParent = this.registrations.get(newParentId);
      if (newParent) {
        if (index !== undefined && index >= 0 && index <= newParent.childrenIds.length) {
          newParent.childrenIds.splice(index, 0, fieldId);
        } else {
          newParent.childrenIds.push(fieldId);
        }
      }
    }
    
    this.notifyChange([fieldId]);
    return true;
  }

  public clear(): void {
    const fieldIds = Array.from(this.registrations.keys());
    this.registrations.clear();
    this.lookup.byId.clear();
    this.lookup.byPath.clear();
    this.lookup.byName.clear();
    this.lookup.byType.clear();
    
    this.notifyChange(fieldIds);
  }

  private notifyChange(fieldIds: string[]): void {
    this.registrySubject.next(new Map(this.registrations));
    this.changeSubject.next(fieldIds);
  }

  public getStats(): any {
    return {
      totalFields: this.registrations.size,
      byType: Object.fromEntries(
        Array.from(this.lookup.byType.entries()).map(([type, ids]) => [type, ids.size])
      ),
      required: this.getRequiredFields().length,
      disabled: this.getDisabledFields().length,
      invalid: this.getInvalidFields().length
    };
  }
}
6. DependencyResolver Service
typescript// core/services/dependency-resolver.service.ts

import { Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { Subscription, combineLatest, merge } from 'rxjs';
import { debounceTime, distinctUntilChanged, startWith } from 'rxjs/operators';

export interface FieldDependency {
  sourceFieldId: string;
  targetFieldId: string;
  type: 'value' | 'visibility' | 'required' | 'disabled' | 'options' | 'validation';
  transform?: (value: any, context?: any) => any;
  debounce?: number;
  bidirectional?: boolean;
}

export interface DependencyContext {
  sourceValue: any;
  targetValue: any;
  allValues: Record<string, any>;
  sourceControl: AbstractControl;
  targetControl: AbstractControl;
}

export type DependencyHandler = (context: DependencyContext) => void;

@Injectable({
  providedIn: 'root'
})
export class DependencyResolver {
  private dependencies = new Map<string, FieldDependency[]>();
  private reverseDependencies = new Map<string, FieldDependency[]>();
  private subscriptions = new Map<string, Subscription>();
  private handlers = new Map<string, DependencyHandler>();
  private controls = new Map<string, AbstractControl>();

  public registerDependency(dependency: FieldDependency): void {
    if (!this.dependencies.has(dependency.targetFieldId)) {
      this.dependencies.set(dependency.targetFieldId, []);
    }
    this.dependencies.get(dependency.targetFieldId)!.push(dependency);
    
    if (!this.reverseDependencies.has(dependency.sourceFieldId)) {
      this.reverseDependencies.set(dependency.sourceFieldId, []);
    }
    this.reverseDependencies.get(dependency.sourceFieldId)!.push(dependency);
    
    if (dependency.bidirectional) {
      const reverse: FieldDependency = {
        sourceFieldId: dependency.targetFieldId,
        targetFieldId: dependency.sourceFieldId,
        type: dependency.type,
        transform: dependency.transform,
        debounce: dependency.debounce,
        bidirectional: false
      };
      
      if (!this.dependencies.has(reverse.targetFieldId)) {
        this.dependencies.set(reverse.targetFieldId, []);
      }
      this.dependencies.get(reverse.targetFieldId)!.push(reverse);
      
      if (!this.reverseDependencies.has(reverse.sourceFieldId)) {
        this.reverseDependencies.set(reverse.sourceFieldId, []);
      }
      this.reverseDependencies.get(reverse.sourceFieldId)!.push(reverse);
    }
  }

  public unregisterDependency(sourceFieldId: string, targetFieldId: string): void {
    const targetDeps = this.dependencies.get(targetFieldId);
    if (targetDeps) {
      const index = targetDeps.findIndex(d => d.sourceFieldId === sourceFieldId);
      if (index > -1) {
        targetDeps.splice(index, 1);
      }
      if (targetDeps.length === 0) {
        this.dependencies.delete(targetFieldId);
      }
    }
    
    const sourceDeps = this.reverseDependencies.get(sourceFieldId);
    if (sourceDeps) {
      const index = sourceDeps.findIndex(d => d.targetFieldId === targetFieldId);
      if (index > -1) {
        sourceDeps.splice(index, 1);
      }
      if (sourceDeps.length === 0) {
        this.reverseDependencies.delete(sourceFieldId);
      }
    }
  }

  public setControl(fieldId: string, control: AbstractControl): void {
    this.controls.set(fieldId, control);
    this.setupDependencySubscriptions(fieldId);
  }

  public removeControl(fieldId: string): void {
    this.controls.delete(fieldId);
    this.clearSubscriptions(fieldId);
  }

  private setupDependencySubscriptions(targetFieldId: string): void {
    this.clearSubscriptions(targetFieldId);
    
    const dependencies = this.dependencies.get(targetFieldId);
    if (!dependencies || dependencies.length === 0) return;
    
    const targetControl = this.controls.get(targetFieldId);
    if (!targetControl) return;
    
    const sourceControls = dependencies
      .map(dep => ({
        dep,
        control: this.controls.get(dep.sourceFieldId)
      }))
      .filter(item => item.control !== undefined);
    
    if (sourceControls.length === 0) return;
    
    const observables = sourceControls.map(item => 
      item.control!.valueChanges.pipe(
        startWith(item.control!.value),
        debounceTime(item.dep.debounce || 100),
        distinctUntilChanged()
      )
    );
    
    const subscription = combineLatest(observables).subscribe(values => {
      sourceControls.forEach((item, index) => {
        const context: DependencyContext = {
          sourceValue: values[index],
          targetValue: targetControl.value,
          allValues: this.getAllValues(),
          sourceControl: item.control!,
          targetControl
        };
        
        this.resolveDependency(item.dep, context);
      });
    });
    
    this.subscriptions.set(targetFieldId, subscription);
  }

  private resolveDependency(dependency: FieldDependency, context: DependencyContext): void {
    const handler = this.handlers.get(dependency.type);
    if (handler) {
      handler(context);
      return;
    }
    
    switch (dependency.type) {
      case 'value':
        this.resolveValueDependency(dependency, context);
        break;
      case 'visibility':
        this.resolveVisibilityDependency(dependency, context);
        break;
      case 'required':
        this.resolveRequiredDependency(dependency, context);
        break;
      case 'disabled':
        this.resolveDisabledDependency(dependency, context);
        break;
      case 'options':
        this.resolveOptionsDependency(dependency, context);
        break;
      case 'validation':
        this.resolveValidationDependency(dependency, context);
        break;
    }
  }

  private resolveValueDependency(dependency: FieldDependency, context: DependencyContext): void {
    let newValue = context.sourceValue;
    
    if (dependency.transform) {
      newValue = dependency.transform(context.sourceValue, context);
    }
    
    if (context.targetControl.value !== newValue) {
      context.targetControl.setValue(newValue, { emitEvent: false });
    }
  }

  private resolveVisibilityDependency(dependency: FieldDependency, context: DependencyContext): void {
    const shouldShow = dependency.transform 
      ? dependency.transform(context.sourceValue, context)
      : !!context.sourceValue;
    
    if (shouldShow) {
      context.targetControl.enable();
    } else {
      context.targetControl.disable();
    }
  }

  private resolveRequiredDependency(dependency: FieldDependency, context: DependencyContext): void {
    const isRequired = dependency.transform
      ? dependency.transform(context.sourceValue, context)
      : !!context.sourceValue;
    
    // This would need ValidatorManager integration
    // For now, just marking as example
    context.targetControl.updateValueAndValidity();
  }

  private resolveDisabledDependency(dependency: FieldDependency, context: DependencyContext): void {
    const shouldDisable = dependency.transform
      ? dependency.transform(context.sourceValue, context)
      : !!context.sourceValue;
    
    if (shouldDisable) {
      context.targetControl.disable();
    } else {
      context.targetControl.enable();
    }
  }

  private resolveOptionsDependency(dependency: FieldDependency, context: DependencyContext): void {
    // This would typically update field options through FieldRegistry
    // Implementation depends on how options are managed in your system
  }

  private resolveValidationDependency(dependency: FieldDependency, context: DependencyContext): void {
    // This would update validation rules based on dependency
    context.targetControl.updateValueAndValidity();
  }

  public registerHandler(type: string, handler: DependencyHandler): void {
    this.handlers.set(type, handler);
  }

  public unregisterHandler(type: string): void {
    this.handlers.delete(type);
  }

  private getAllValues(): Record<string, any> {
    const values: Record<string, any> = {};
    
    this.controls.forEach((control, fieldId) => {
      values[fieldId] = control.value;
    });
    
    return values;
  }

  private clearSubscriptions(fieldId: string): void {
    const subscription = this.subscriptions.get(fieldId);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(fieldId);
    }
  }

  public getDependencies(targetFieldId: string): FieldDependency[] {
    return this.dependencies.get(targetFieldId) || [];
  }

  public getDependents(sourceFieldId: string): FieldDependency[] {
    return this.reverseDependencies.get(sourceFieldId) || [];
  }

  public hasDependencies(fieldId: string): boolean {
    return this.dependencies.has(fieldId) && this.dependencies.get(fieldId)!.length > 0;
  }

  public hasDependents(fieldId: string): boolean {
    return this.reverseDependencies.has(fieldId) && this.reverseDependencies.get(fieldId)!.length > 0;
  }

  public getDependencyGraph(): Map<string, string[]> {
    const graph = new Map<string, string[]>();
    
    this.dependencies.forEach((deps, targetId) => {
      graph.set(targetId, deps.map(d => d.sourceFieldId));
    });
    
    return graph;
  }

  public detectCycles(): string[][] {
    const cycles: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    
    const detectCyclesUtil = (fieldId: string, path: string[]): void => {
      visited.add(fieldId);
      recursionStack.add(fieldId);
      path.push(fieldId);
      
      const dependencies = this.dependencies.get(fieldId) || [];
      
      for (const dep of dependencies) {
        if (!visited.has(dep.sourceFieldId)) {
          detectCyclesUtil(dep.sourceFieldId, [...path]);
        } else if (recursionStack.has(dep.sourceFieldId)) {
          const cycleStart = path.indexOf(dep.sourceFieldId);
          cycles.push(path.slice(cycleStart));
        }
      }
      
      recursionStack.delete(fieldId);
    };
    
    this.dependencies.forEach((_, fieldId) => {
      if (!visited.has(fieldId)) {
        detectCyclesUtil(fieldId, []);
      }
    });
    
    return cycles;
  }

  public clear(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.dependencies.clear();
    this.reverseDependencies.clear();
    this.subscriptions.clear();
    this.handlers.clear();
    this.controls.clear();
  }
}