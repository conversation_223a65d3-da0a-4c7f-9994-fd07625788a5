import { Injectable } from '@angular/core';
import {
  ValidatorFn,
  AsyncValidatorFn,
  Validators,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { ValidatorManager } from './validator-manager.util';

export interface ValidatorDefinition {
  name: string;
  factory: (params?: any) => ValidatorFn;
  message?: string | ((params?: any) => string);
}

export interface AsyncValidatorDefinition {
  name: string;
  factory: (params?: any) => AsyncValidatorFn;
  message?: string | ((params?: any) => string);
}

@Injectable()
export class ValidatorRegistry {
  private readonly validators = new Map<string, ValidatorDefinition>();
  private readonly asyncValidators = new Map<
    string,
    AsyncValidatorDefinition
  >();
  private readonly errorMessages = new Map<
    string,
    string | ((params?: any) => string)
  >();
  private readonly validatorManager = new ValidatorManager();

  constructor() {
    this.registerBuiltInValidators();
  }

  private registerBuiltInValidators(): void {
    this.registerValidator({
      name: 'required',
      factory: () => Validators.required,
      message: 'This field is required',
    });

    this.registerValidator({
      name: 'email',
      factory: () => Validators.email,
      message: 'Please enter a valid email address',
    });

    this.registerValidator({
      name: 'min',
      factory: (params) => Validators.min(params?.value ?? params),
      message: (params) => `Value must be at least ${params?.value ?? params}`,
    });

    this.registerValidator({
      name: 'max',
      factory: (params) => Validators.max(params?.value ?? params),
      message: (params) => `Value must not exceed ${params?.value ?? params}`,
    });

    this.registerValidator({
      name: 'minLength',
      factory: (params) => Validators.minLength(params?.value ?? params),
      message: (params) =>
        `Minimum ${params?.value ?? params} characters required`,
    });

    this.registerValidator({
      name: 'maxLength',
      factory: (params) => Validators.maxLength(params?.value ?? params),
      message: (params) =>
        `Maximum ${params?.value ?? params} characters allowed`,
    });

    this.registerValidator({
      name: 'pattern',
      factory: (params) => Validators.pattern(params?.value ?? params),
      message: 'Invalid format',
    });

    this.registerValidator({
      name: 'url',
      factory:
        () =>
        (control: AbstractControl): ValidationErrors | null => {
          if (!control.value) return null;
          try {
            new URL(control.value);
            return null;
          } catch {
            return { url: true };
          }
        },
      message: 'Please enter a valid URL',
    });

    this.registerValidator({
      name: 'phone',
      factory:
        () =>
        (control: AbstractControl): ValidationErrors | null => {
          if (!control.value) return null;
          const pattern =
            /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/;
          return pattern.test(control.value) ? null : { phone: true };
        },
      message: 'Please enter a valid phone number',
    });

    this.registerValidator({
      name: 'alphanumeric',
      factory:
        () =>
        (control: AbstractControl): ValidationErrors | null => {
          if (!control.value) return null;
          return /^[a-zA-Z0-9]+$/.test(control.value)
            ? null
            : { alphanumeric: true };
        },
      message: 'Only letters and numbers are allowed',
    });

    this.registerValidator({
      name: 'numeric',
      factory:
        () =>
        (control: AbstractControl): ValidationErrors | null => {
          if (!control.value) return null;
          return /^\d+$/.test(control.value) ? null : { numeric: true };
        },
      message: 'Only numbers are allowed',
    });

    this.registerValidator({
      name: 'strongPassword',
      factory:
        () =>
        (control: AbstractControl): ValidationErrors | null => {
          if (!control.value) return null;
          const hasUpper = /[A-Z]/.test(control.value);
          const hasLower = /[a-z]/.test(control.value);
          const hasNumber = /\d/.test(control.value);
          const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(control.value);
          return hasUpper && hasLower && hasNumber && hasSpecial
            ? null
            : { strongPassword: true };
        },
      message:
        'Password must contain uppercase, lowercase, number and special character',
    });

    this.registerValidator({
      name: 'equalTo',
      factory:
        (params) =>
        (control: AbstractControl): ValidationErrors | null => {
          if (!control.parent) return null;
          const field = control.parent.get(params?.field ?? params);
          if (!field) return null;
          return control.value === field.value
            ? null
            : { equalTo: { field: params?.field ?? params } };
        },
      message: (params) => `Must match ${params?.field ?? params}`,
    });

    this.registerValidator({
      name: 'json',
      factory:
        () =>
        (control: AbstractControl): ValidationErrors | null => {
          if (!control.value) return null;
          try {
            JSON.parse(control.value);
            return null;
          } catch {
            return { json: true };
          }
        },
      message: 'Invalid JSON format',
    });
  }

  public registerValidator(definition: ValidatorDefinition): void {
    this.validators.set(definition.name, definition);
    if (definition.message) {
      this.errorMessages.set(definition.name, definition.message);
    }
  }

  public registerAsyncValidator(definition: AsyncValidatorDefinition): void {
    this.asyncValidators.set(definition.name, definition);
    if (definition.message) {
      this.errorMessages.set(definition.name, definition.message);
    }
  }

  public getValidator(name: string): ValidatorDefinition | undefined {
    return this.validators.get(name);
  }

  public getAsyncValidator(name: string): AsyncValidatorDefinition | undefined {
    return this.asyncValidators.get(name);
  }

  public createValidator(name: string, params?: any): ValidatorFn | null {
    const definition = this.validators.get(name);
    return definition ? definition.factory(params) : null;
  }

  public createAsyncValidator(
    name: string,
    params?: any
  ): AsyncValidatorFn | null {
    const definition = this.asyncValidators.get(name);
    return definition ? definition.factory(params) : null;
  }

  public getErrorMessage(name: string, params?: any): string {
    const message = this.errorMessages.get(name);
    if (typeof message === 'function') {
      return message(params);
    }
    return message || `Validation failed: ${name}`;
  }

  public applyValidatorToControl(
    control: AbstractControl,
    name: string,
    params?: any
  ): boolean {
    const validator = this.createValidator(name, params);
    if (validator) {
      this.validatorManager.addValidator(control, name, validator);
      return true;
    }
    return false;
  }

  public applyAsyncValidatorToControl(
    control: AbstractControl,
    name: string,
    params?: any
  ): boolean {
    const validator = this.createAsyncValidator(name, params);
    if (validator) {
      this.validatorManager.addAsyncValidator(control, name, validator);
      return true;
    }
    return false;
  }

  public removeValidatorFromControl(
    control: AbstractControl,
    name: string
  ): void {
    this.validatorManager.removeValidator(control, name);
  }

  public removeAsyncValidatorFromControl(
    control: AbstractControl,
    name: string
  ): void {
    this.validatorManager.removeAsyncValidator(control, name);
  }

  public getValidatorManager(): ValidatorManager {
    return this.validatorManager;
  }

  public getAllValidatorNames(): string[] {
    return Array.from(this.validators.keys());
  }

  public getAllAsyncValidatorNames(): string[] {
    return Array.from(this.asyncValidators.keys());
  }

  public hasValidator(name: string): boolean {
    return this.validators.has(name);
  }

  public hasAsyncValidator(name: string): boolean {
    return this.asyncValidators.has(name);
  }
}
