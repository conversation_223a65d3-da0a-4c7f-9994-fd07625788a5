import { Injectable } from '@angular/core';
import { AbstractControl, FormGroup, FormArray } from '@angular/forms';

export interface PathSegment {
  type: 'object' | 'array';
  key: string | number;
}

@Injectable()
export class PathResolver {
  public parsePath(path: string): PathSegment[] {
    if (!path) return [];

    const segments: PathSegment[] = [];
    const parts = path.split('.');

    for (const part of parts) {
      if (/^\d+$/.test(part)) {
        segments.push({ type: 'array', key: parseInt(part, 10) });
      } else {
        segments.push({ type: 'object', key: part });
      }
    }

    return segments;
  }

  public buildPath(segments: PathSegment[]): string {
    return segments.map((s) => s.key).join('.');
  }

  public getControl(
    root: AbstractControl,
    path: string
  ): AbstractControl | null {
    if (!path) return root;

    const segments = this.parsePath(path);
    let current: AbstractControl | null = root;

    for (const segment of segments) {
      if (!current) return null;

      if (segment.type === 'object' && current instanceof FormGroup) {
        current = current.controls[segment.key as string] || null;
      } else if (segment.type === 'array' && current instanceof FormArray) {
        current = current.at(segment.key as number) || null;
      } else {
        return null;
      }
    }

    return current;
  }

  public getValue(root: any, path: string): any {
    if (!path) return root;

    const segments = this.parsePath(path);
    let current = root;

    for (const segment of segments) {
      if (current == null) return undefined;
      current = current[segment.key];
    }

    return current;
  }

  public setValue(root: any, path: string, value: any): boolean {
    if (!path) return false;

    const segments = this.parsePath(path);
    if (segments.length === 0) return false;

    let current = root;

    for (let i = 0; i < segments.length - 1; i++) {
      const segment = segments[i];

      if (current[segment.key] == null) {
        current[segment.key] = segment.type === 'array' ? [] : {};
      }

      current = current[segment.key];
    }

    const lastSegment = segments[segments.length - 1];
    current[lastSegment.key] = value;

    return true;
  }

  public getParentPath(path: string): string {
    const segments = this.parsePath(path);
    if (segments.length === 0) return '';

    segments.pop();
    return this.buildPath(segments);
  }

  public getLastSegment(path: string): string {
    const segments = this.parsePath(path);
    if (segments.length === 0) return '';

    return String(segments[segments.length - 1].key);
  }

  public joinPaths(...paths: string[]): string {
    return paths.filter((p) => p).join('.');
  }

  public isArrayPath(path: string): boolean {
    const segments = this.parsePath(path);
    return (
      segments.length > 0 && segments[segments.length - 1].type === 'array'
    );
  }

  public normalizePath(path: string): string {
    return path.replace(/\[(\d+)\]/g, '.$1').replace(/^\./, '');
  }

  public getRelativePath(basePath: string, fullPath: string): string {
    if (!fullPath.startsWith(basePath)) return fullPath;

    const relative = fullPath.substring(basePath.length);
    return relative.startsWith('.') ? relative.substring(1) : relative;
  }

  public isChildPath(parentPath: string, childPath: string): boolean {
    return childPath.startsWith(parentPath + '.');
  }

  public getAllPaths(obj: any, prefix = ''): string[] {
    const paths: string[] = [];

    if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach((key) => {
        const fullPath = prefix ? `${prefix}.${key}` : key;
        paths.push(fullPath);

        if (typeof obj[key] === 'object' && obj[key] !== null) {
          paths.push(...this.getAllPaths(obj[key], fullPath));
        }
      });
    }

    return paths;
  }
}
